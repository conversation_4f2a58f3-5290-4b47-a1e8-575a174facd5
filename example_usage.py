#!/usr/bin/env python3
"""
指纹浏览器集成使用示例
演示如何使用指纹浏览器进行视频下载和其他操作
"""

import os
import sys
from fingerprint_browser import create_browser_manager
from url import download_video

def main():
    print("🎬 指纹浏览器集成示例")
    print("=" * 60)
    
    # 创建指纹浏览器管理器
    print("1️⃣ 创建指纹浏览器管理器...")
    manager = create_browser_manager()
    
    # 健康检查
    print("\n2️⃣ 检查指纹浏览器服务状态...")
    if not manager.health_check():
        print("❌ 指纹浏览器服务不可用，请确保:")
        print("   • 指纹浏览器软件已启动")
        print("   • 服务运行在 127.0.0.1:50000")
        print("   • API密钥正确")
        return
    
    # 获取配置文件列表
    print("\n3️⃣ 获取可用配置文件...")
    profiles = manager.list_profiles()
    if profiles and 'profiles' in profiles:
        profile_list = profiles['profiles']
        print(f"✅ 找到 {len(profile_list)} 个配置文件:")
        for i, profile in enumerate(profile_list[:5], 1):  # 只显示前5个
            profile_id = profile.get('id', 'Unknown')
            profile_name = profile.get('name', 'Unnamed')
            print(f"   {i}. {profile_name} (ID: {profile_id})")
        
        if len(profile_list) > 5:
            print(f"   ... 还有 {len(profile_list) - 5} 个配置文件")
    else:
        print("⚠️  未找到配置文件或获取失败")
        profile_list = []
    
    # 交互式选择操作
    print("\n4️⃣ 选择操作:")
    print("   1. 测试浏览器会话启动/停止")
    print("   2. 使用指纹浏览器下载视频")
    print("   3. 创建新的配置文件")
    print("   4. 查看活动会话")
    print("   5. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == "1":
                test_browser_session(manager, profile_list)
            elif choice == "2":
                download_with_fingerprint_browser(manager, profile_list)
            elif choice == "3":
                create_new_profile(manager)
            elif choice == "4":
                show_active_sessions(manager)
            elif choice == "5":
                print("👋 退出程序")
                break
            else:
                print("❌ 无效选择，请输入 1-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 操作出错: {e}")
    
    # 清理所有会话
    print("\n🧹 清理活动会话...")
    manager.stop_all_sessions()
    print("✅ 程序结束")

def test_browser_session(manager, profile_list):
    """测试浏览器会话启动和停止"""
    print("\n🧪 测试浏览器会话...")
    
    # 选择配置文件
    profile_id = None
    if profile_list:
        use_profile = input("是否使用特定配置文件? (y/N): ").strip().lower()
        if use_profile == 'y':
            try:
                index = int(input(f"选择配置文件 (1-{min(5, len(profile_list))}): ")) - 1
                if 0 <= index < len(profile_list):
                    profile_id = profile_list[index].get('id')
                    print(f"✅ 选择配置文件: {profile_list[index].get('name')} ({profile_id})")
            except (ValueError, IndexError):
                print("⚠️  无效选择，使用默认配置")
    
    # 启动会话
    session = manager.start_browser(profile_id)
    if session:
        session_id = session.get('session_id')
        print(f"✅ 会话启动成功: {session_id}")
        
        # 获取会话信息
        info = manager.get_browser_info(session_id)
        if info:
            print(f"📋 会话信息: {info}")
        
        # 等待用户确认
        input("⏳ 按回车键停止会话...")
        
        # 停止会话
        if manager.stop_browser(session_id):
            print("✅ 会话已成功停止")
        else:
            print("❌ 停止会话失败")
    else:
        print("❌ 会话启动失败")

def download_with_fingerprint_browser(manager, profile_list):
    """使用指纹浏览器下载视频"""
    print("\n📥 使用指纹浏览器下载视频...")
    
    # 输入视频URL
    default_url = 'https://resources-sgp-auth.flextv.cc/wz/mp4/a/b/abd9961b60a8c28d3a4b19dec28a2335/b5d165f76cd08855282f1823ad44b405/1080p-max.mp4'
    video_url = input(f"请输入视频URL (回车使用默认): ").strip()
    if not video_url:
        video_url = default_url
        print(f"使用默认URL: {video_url}")
    
    # 选择配置文件
    profile_id = None
    if profile_list:
        use_profile = input("是否使用特定配置文件? (y/N): ").strip().lower()
        if use_profile == 'y':
            try:
                index = int(input(f"选择配置文件 (1-{min(5, len(profile_list))}): ")) - 1
                if 0 <= index < len(profile_list):
                    profile_id = profile_list[index].get('id')
                    print(f"✅ 选择配置文件: {profile_list[index].get('name')} ({profile_id})")
            except (ValueError, IndexError):
                print("⚠️  无效选择，使用默认配置")
    
    # 输入文件名
    filename = input("请输入保存文件名 (回车使用默认): ").strip()
    if not filename:
        filename = "fingerprint_downloaded_video.mp4"
    
    # 开始下载
    print(f"\n🚀 开始下载...")
    success = download_video(
        url=video_url,
        filename=filename,
        use_fingerprint_browser=True,
        profile_id=profile_id
    )
    
    if success:
        print(f"🎉 下载成功: {filename}")
        if os.path.exists(filename):
            size_mb = os.path.getsize(filename) / (1024 * 1024)
            print(f"📁 文件大小: {size_mb:.2f} MB")
    else:
        print("💥 下载失败")

def create_new_profile(manager):
    """创建新的配置文件"""
    print("\n🆕 创建新配置文件...")
    
    name = input("请输入配置文件名称: ").strip()
    if not name:
        print("❌ 配置文件名称不能为空")
        return
    
    # 可以添加更多配置选项
    print("📝 配置选项 (可选，直接回车跳过):")
    user_agent = input("  User-Agent: ").strip()
    proxy = input("  代理地址 (格式: host:port): ").strip()
    
    kwargs = {}
    if user_agent:
        kwargs['user_agent'] = user_agent
    if proxy:
        kwargs['proxy'] = proxy
    
    # 创建配置文件
    result = manager.create_profile(name, **kwargs)
    if result:
        profile_id = result.get('id', 'Unknown')
        print(f"✅ 配置文件创建成功: {name} (ID: {profile_id})")
    else:
        print("❌ 配置文件创建失败")

def show_active_sessions(manager):
    """显示活动会话"""
    print("\n📊 活动会话列表...")
    
    sessions = manager.get_active_sessions()
    if sessions:
        print(f"✅ 当前有 {len(sessions)} 个活动会话:")
        for session_id, session_info in sessions.items():
            profile_id = session_info.get('profile_id', 'Default')
            start_time = session_info.get('start_time', 0)
            duration = time.time() - start_time if start_time else 0
            print(f"   • {session_id} (配置: {profile_id}, 运行: {duration:.1f}s)")
        
        # 询问是否停止某个会话
        stop_session = input("\n是否停止某个会话? (y/N): ").strip().lower()
        if stop_session == 'y':
            session_id = input("请输入要停止的会话ID: ").strip()
            if session_id in sessions:
                if manager.stop_browser(session_id):
                    print(f"✅ 会话 {session_id} 已停止")
                else:
                    print(f"❌ 停止会话 {session_id} 失败")
            else:
                print("❌ 会话ID不存在")
    else:
        print("📭 当前没有活动会话")

if __name__ == "__main__":
    import time
    main()
