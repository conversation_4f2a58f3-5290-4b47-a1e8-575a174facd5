#!/usr/bin/env python3
"""
简化版视频链接抓取器
使用指纹浏览器 + requests 获取视频页面并提取视频链接
"""

import requests
import re
import json
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
from fingerprint_browser import create_browser_manager

class SimpleVideoScraper:
    """简化版视频抓取器"""
    
    def __init__(self):
        self.browser_manager = create_browser_manager()
        self.session = requests.Session()
        
    def setup_session_with_fingerprint(self) -> bool:
        """使用指纹浏览器设置请求会话"""
        print("🚀 启动指纹浏览器...")
        
        # 健康检查
        if not self.browser_manager.health_check():
            print("❌ Roxy Browser服务不可用")
            return False
        
        # 启动浏览器会话
        session_data = self.browser_manager.start_browser()
        if not session_data:
            print("❌ 无法启动浏览器会话")
            return False
            
        print(f"✅ 浏览器会话启动成功")
        print(f"   WebSocket: {session_data.get('ws')}")
        print(f"   HTTP调试端口: {session_data.get('http')}")
        
        # 设置请求头模拟真实浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"'
        })
        
        self.session_data = session_data
        return True
    
    def extract_video_urls_from_html(self, html_content: str, base_url: str) -> List[Dict[str, str]]:
        """从HTML内容中提取视频URL"""
        video_urls = []
        
        # 各种视频URL匹配模式
        patterns = [
            # 直接的视频文件URL
            r'"(https?://[^"]*\.(?:mp4|m3u8|ts|flv|avi|mov|wmv|webm)[^"]*)"',
            r"'(https?://[^']*\.(?:mp4|m3u8|ts|flv|avi|mov|wmv|webm)[^']*)'",
            
            # video标签的src属性
            r'<video[^>]*src=["\']([^"\']+)["\']',
            r'<source[^>]*src=["\']([^"\']+)["\']',
            
            # JavaScript中的视频URL
            r'video_url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'videoUrl["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'src["\']?\s*[:=]\s*["\']([^"\']*\.(?:mp4|m3u8)[^"\']*)["\']',
            r'url["\']?\s*[:=]\s*["\']([^"\']*\.(?:mp4|m3u8)[^"\']*)["\']',
            
            # 常见的视频播放器配置
            r'file["\']?\s*[:=]\s*["\']([^"\']*\.(?:mp4|m3u8)[^"\']*)["\']',
            r'source["\']?\s*[:=]\s*["\']([^"\']*\.(?:mp4|m3u8)[^"\']*)["\']',
            
            # FlexTV特定模式
            r'resources-[^"\']*\.flextv\.cc[^"\']*\.(?:mp4|m3u8)[^"\']*',
            r'https?://[^"\']*flextv[^"\']*\.(?:mp4|m3u8)[^"\']*',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                url = match if isinstance(match, str) else match[0]
                
                # 清理URL
                url = url.strip()
                if not url:
                    continue
                    
                # 转换为绝对URL
                if url.startswith('//'):
                    url = 'https:' + url
                elif url.startswith('/'):
                    url = urljoin(base_url, url)
                elif not url.startswith('http'):
                    continue
                
                # 检查是否是有效的视频URL
                if any(ext in url.lower() for ext in ['.mp4', '.m3u8', '.ts', '.flv']):
                    video_urls.append({
                        'url': url,
                        'type': self._get_video_type(url),
                        'quality': self._extract_quality(url)
                    })
        
        # 去重
        seen_urls = set()
        unique_urls = []
        for video in video_urls:
            if video['url'] not in seen_urls:
                seen_urls.add(video['url'])
                unique_urls.append(video)
        
        return unique_urls
    
    def _get_video_type(self, url: str) -> str:
        """获取视频类型"""
        url_lower = url.lower()
        if '.mp4' in url_lower:
            return 'mp4'
        elif '.m3u8' in url_lower:
            return 'm3u8'
        elif '.ts' in url_lower:
            return 'ts'
        elif '.flv' in url_lower:
            return 'flv'
        else:
            return 'unknown'
    
    def _extract_quality(self, url: str) -> str:
        """从URL中提取视频质量"""
        quality_patterns = [
            r'(\d+p)',  # 720p, 1080p等
            r'(\d+)p',  # 720p, 1080p等
            r'(high|medium|low)',  # 质量描述
            r'(hd|sd)',  # HD, SD
        ]
        
        for pattern in quality_patterns:
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return 'unknown'
    
    def scrape_video_page(self, page_url: str) -> Dict:
        """抓取视频页面"""
        try:
            print(f"🌐 访问页面: {page_url}")
            
            # 发送请求
            response = self.session.get(page_url, timeout=30)
            response.raise_for_status()
            
            print(f"✅ 页面加载成功 (状态码: {response.status_code})")
            print(f"   内容长度: {len(response.text)} 字符")
            
            # 提取视频URL
            video_urls = self.extract_video_urls_from_html(response.text, page_url)
            
            return {
                'success': True,
                'page_url': page_url,
                'video_urls': video_urls,
                'count': len(video_urls),
                'page_title': self._extract_title(response.text)
            }
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _extract_title(self, html_content: str) -> str:
        """提取页面标题"""
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
        if title_match:
            return title_match.group(1).strip()
        return 'Unknown'
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'session_data') and self.session_data:
                session_id = self.session_data.get('session_id')
                if session_id:
                    self.browser_manager.stop_browser(session_id)
        except Exception as e:
            print(f"⚠️  清理资源时出错: {e}")

def main():
    """主函数"""
    print("🎬 简化版视频链接抓取器")
    print("=" * 50)
    
    # 获取用户输入
    video_url = input("请输入视频页面URL: ").strip()
    if not video_url:
        # 使用示例URL
        video_url = "https://resources-sgp-auth.flextv.cc/wz/mp4/a/b/abd9961b60a8c28d3a4b19dec28a2335/b5d165f76cd08855282f1823ad44b405/1080p-max.mp4"
        print(f"使用示例URL: {video_url}")
    
    # 创建抓取器
    scraper = SimpleVideoScraper()
    
    try:
        # 设置指纹浏览器会话
        if not scraper.setup_session_with_fingerprint():
            print("❌ 指纹浏览器设置失败，使用普通模式")
        
        print(f"\n🚀 开始抓取视频页面...")
        result = scraper.scrape_video_page(video_url)
        
        if result['success']:
            print(f"\n✅ 抓取成功！")
            print(f"   页面标题: {result.get('page_title', 'Unknown')}")
            print(f"   找到 {result['count']} 个视频链接:")
            print("=" * 50)
            
            for i, video in enumerate(result['video_urls'], 1):
                print(f"\n{i}. 视频链接:")
                print(f"   URL: {video['url']}")
                print(f"   类型: {video['type']}")
                print(f"   质量: {video['quality']}")
            
            # 保存结果
            output_file = "simple_video_links.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n💾 结果已保存到: {output_file}")
            
            # 如果找到视频链接，询问是否下载
            if result['video_urls']:
                download = input("\n是否使用找到的链接下载视频? (y/N): ").strip().lower()
                if download == 'y':
                    # 选择最佳视频链接
                    best_video = None
                    
                    # 优先选择mp4格式的高质量视频
                    for video in result['video_urls']:
                        if video['type'] == 'mp4':
                            if '1080p' in video['quality'] or 'high' in video['quality']:
                                best_video = video
                                break
                    
                    # 如果没有找到高质量mp4，选择任意mp4
                    if not best_video:
                        for video in result['video_urls']:
                            if video['type'] == 'mp4':
                                best_video = video
                                break
                    
                    # 如果没有mp4，选择第一个
                    if not best_video:
                        best_video = result['video_urls'][0]
                    
                    print(f"\n📥 开始下载: {best_video['url']}")
                    print(f"   类型: {best_video['type']}")
                    print(f"   质量: {best_video['quality']}")
                    
                    # 调用现有的下载函数
                    from url import download_video
                    success = download_video(
                        url=best_video['url'],
                        filename=f"scraped_video_{best_video['type']}.{best_video['type']}",
                        use_fingerprint_browser=True
                    )
                    
                    if success:
                        print("🎉 视频下载完成！")
                    else:
                        print("💥 视频下载失败！")
        else:
            print(f"\n❌ 抓取失败: {result.get('error', '未知错误')}")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
    finally:
        scraper.cleanup()

if __name__ == "__main__":
    main()
