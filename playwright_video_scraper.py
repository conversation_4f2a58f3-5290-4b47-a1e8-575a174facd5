#!/usr/bin/env python3
"""
使用Playwright调用指纹浏览器获取视频链接
通过Roxy Browser的WebSocket连接控制浏览器，点击视频详情页获取视频链接
"""

import asyncio
import json
import re
from typing import Optional, List, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from fingerprint_browser import create_browser_manager
from urllib.parse import urljoin, urlparse

class PlaywrightVideoScraper:
    """使用Playwright控制指纹浏览器的视频抓取器"""
    
    def __init__(self):
        self.browser_manager = create_browser_manager()
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.session_data = None
        
    async def start_fingerprint_browser(self, dir_id: Optional[str] = None) -> bool:
        """启动指纹浏览器并获取连接信息"""
        print("🚀 启动指纹浏览器...")
        
        # 健康检查
        if not self.browser_manager.health_check():
            print("❌ Roxy Browser服务不可用")
            return False
        
        # 启动浏览器会话
        self.session_data = self.browser_manager.start_browser(dir_id)
        if not self.session_data:
            print("❌ 无法启动浏览器会话")
            return False
            
        print(f"✅ 浏览器会话启动成功")
        print(f"   WebSocket: {self.session_data.get('ws')}")
        print(f"   HTTP调试端口: {self.session_data.get('http')}")
        
        return True
    
    async def connect_playwright(self) -> bool:
        """连接Playwright到指纹浏览器"""
        if not self.session_data:
            print("❌ 没有活动的浏览器会话")
            return False
            
        try:
            playwright = await async_playwright().start()
            
            # 从WebSocket URL提取调试端口
            ws_url = self.session_data.get('ws', '')
            if not ws_url:
                print("❌ 未找到WebSocket URL")
                return False
            
            # 解析调试端口
            import re
            port_match = re.search(r':(\d+)/', ws_url)
            if not port_match:
                print("❌ 无法解析调试端口")
                return False
                
            debug_port = port_match.group(1)
            cdp_url = f"http://127.0.0.1:{debug_port}"
            
            print(f"🔗 连接到Chrome DevTools: {cdp_url}")
            
            # 连接到现有的浏览器实例
            self.browser = await playwright.chromium.connect_over_cdp(cdp_url)
            
            # 获取现有的上下文和页面，或创建新的
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
                pages = self.context.pages
                if pages:
                    self.page = pages[0]
                else:
                    self.page = await self.context.new_page()
            else:
                self.context = await self.browser.new_context()
                self.page = await self.context.new_page()
            
            print("✅ Playwright连接成功")
            return True
            
        except Exception as e:
            print(f"❌ Playwright连接失败: {e}")
            return False
    
    async def navigate_to_site(self, url: str) -> bool:
        """导航到指定网站"""
        if not self.page:
            print("❌ 页面未初始化")
            return False
            
        try:
            print(f"🌐 导航到: {url}")
            await self.page.goto(url, wait_until='networkidle', timeout=30000)
            
            # 等待页面加载完成
            await self.page.wait_for_load_state('domcontentloaded')
            await asyncio.sleep(2)  # 额外等待确保页面完全加载
            
            current_url = self.page.url
            print(f"✅ 页面加载完成: {current_url}")
            return True
            
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
    
    async def find_video_links(self) -> List[Dict[str, str]]:
        """查找页面上的视频链接"""
        if not self.page:
            return []
            
        try:
            print("🔍 查找视频链接...")
            
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            
            # 查找视频相关的链接
            video_selectors = [
                'a[href*="video"]',
                'a[href*="watch"]',
                'a[href*="play"]',
                'a[href*=".mp4"]',
                'a[href*=".m3u8"]',
                '.video-item a',
                '.video-card a',
                '.video-link',
                '[data-video-id]',
                '.play-button',
                '.video-thumbnail a'
            ]
            
            video_links = []
            
            for selector in video_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        href = await element.get_attribute('href')
                        title = await element.get_attribute('title') or await element.inner_text()
                        
                        if href:
                            # 转换为绝对URL
                            absolute_url = urljoin(self.page.url, href)
                            video_links.append({
                                'url': absolute_url,
                                'title': title.strip() if title else 'Unknown',
                                'selector': selector
                            })
                except:
                    continue
            
            # 去重
            seen_urls = set()
            unique_links = []
            for link in video_links:
                if link['url'] not in seen_urls:
                    seen_urls.add(link['url'])
                    unique_links.append(link)
            
            print(f"✅ 找到 {len(unique_links)} 个视频链接")
            return unique_links
            
        except Exception as e:
            print(f"❌ 查找视频链接失败: {e}")
            return []
    
    async def click_video_and_get_source(self, video_link: Dict[str, str]) -> Optional[str]:
        """点击视频链接并获取视频源地址"""
        if not self.page:
            return None
            
        try:
            print(f"🎬 点击视频: {video_link['title']}")
            print(f"   URL: {video_link['url']}")
            
            # 监听网络请求，捕获视频文件
            video_urls = []
            
            def handle_response(response):
                url = response.url
                content_type = response.headers.get('content-type', '')
                
                # 检查是否是视频文件
                if (any(ext in url.lower() for ext in ['.mp4', '.m3u8', '.ts', '.flv', '.avi']) or
                    'video' in content_type.lower() or
                    'application/vnd.apple.mpegurl' in content_type.lower()):
                    video_urls.append({
                        'url': url,
                        'content_type': content_type,
                        'status': response.status
                    })
                    print(f"📹 发现视频资源: {url}")
            
            # 开始监听网络请求
            self.page.on('response', handle_response)
            
            # 导航到视频页面
            await self.page.goto(video_link['url'], wait_until='networkidle', timeout=30000)
            
            # 等待页面加载
            await asyncio.sleep(3)
            
            # 尝试查找并点击播放按钮
            play_selectors = [
                '.play-button',
                '.video-play-btn',
                '[data-play]',
                'button[aria-label*="play"]',
                '.play-icon',
                'video',
                '.player-play-btn'
            ]
            
            for selector in play_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        print(f"🎯 点击播放按钮: {selector}")
                        await element.click()
                        await asyncio.sleep(2)
                        break
                except:
                    continue
            
            # 等待视频加载
            await asyncio.sleep(5)
            
            # 尝试从页面源码中提取视频URL
            page_content = await self.page.content()
            
            # 使用正则表达式查找视频URL
            video_patterns = [
                r'"(https?://[^"]*\.mp4[^"]*)"',
                r"'(https?://[^']*\.mp4[^']*)'",
                r'"(https?://[^"]*\.m3u8[^"]*)"',
                r"'(https?://[^']*\.m3u8[^']*)'",
                r'src="([^"]*\.mp4[^"]*)"',
                r"src='([^']*\.mp4[^']*)'",
                r'url:\s*["\']([^"\']*\.mp4[^"\']*)["\']',
                r'video_url["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in video_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                for match in matches:
                    if match and match.startswith('http'):
                        video_urls.append({
                            'url': match,
                            'content_type': 'video/mp4',
                            'status': 200,
                            'source': 'page_content'
                        })
            
            # 停止监听网络请求
            self.page.remove_listener('response', handle_response)
            
            if video_urls:
                print(f"✅ 找到 {len(video_urls)} 个视频资源")
                # 返回第一个有效的视频URL
                for video in video_urls:
                    if video['status'] == 200:
                        return video['url']
                return video_urls[0]['url']
            else:
                print("❌ 未找到视频资源")
                return None
                
        except Exception as e:
            print(f"❌ 获取视频源失败: {e}")
            return None
    
    async def scrape_videos(self, site_url: str, max_videos: int = 5) -> List[Dict[str, Any]]:
        """抓取网站上的视频信息"""
        results = []
        
        try:
            # 启动指纹浏览器
            if not await self.start_fingerprint_browser():
                return results
            
            # 连接Playwright
            if not await self.connect_playwright():
                return results
            
            # 导航到网站
            if not await self.navigate_to_site(site_url):
                return results
            
            # 查找视频链接
            video_links = await self.find_video_links()
            
            if not video_links:
                print("❌ 未找到视频链接")
                return results
            
            # 处理前几个视频链接
            for i, video_link in enumerate(video_links[:max_videos]):
                print(f"\n📺 处理视频 {i+1}/{min(len(video_links), max_videos)}")
                
                # 获取视频源地址
                video_source = await self.click_video_and_get_source(video_link)
                
                result = {
                    'title': video_link['title'],
                    'page_url': video_link['url'],
                    'video_source': video_source,
                    'success': video_source is not None
                }
                
                results.append(result)
                
                # 返回主页面继续处理下一个视频
                await self.page.go_back()
                await asyncio.sleep(2)
            
            return results
            
        except Exception as e:
            print(f"❌ 抓取过程出错: {e}")
            return results
        
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                await self.browser.close()
            
            if self.session_data and self.browser_manager:
                session_id = self.session_data.get('session_id')
                if session_id:
                    self.browser_manager.stop_browser(session_id)
                    
        except Exception as e:
            print(f"⚠️  清理资源时出错: {e}")

async def main():
    """主函数"""
    print("🎬 Playwright + 指纹浏览器视频抓取工具")
    print("=" * 60)
    
    # 获取用户输入
    site_url = input("请输入要抓取的网站URL: ").strip()
    if not site_url:
        site_url = "https://example-video-site.com"  # 默认示例
        print(f"使用默认URL: {site_url}")
    
    max_videos = input("请输入要抓取的视频数量 (默认5): ").strip()
    try:
        max_videos = int(max_videos) if max_videos else 5
    except ValueError:
        max_videos = 5
    
    # 创建抓取器
    scraper = PlaywrightVideoScraper()
    
    try:
        # 开始抓取
        results = await scraper.scrape_videos(site_url, max_videos)
        
        # 显示结果
        print(f"\n📊 抓取结果 ({len(results)} 个视频)")
        print("=" * 60)
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['title']}")
            print(f"   页面URL: {result['page_url']}")
            if result['success']:
                print(f"   ✅ 视频源: {result['video_source']}")
            else:
                print(f"   ❌ 未找到视频源")
        
        # 保存结果到文件
        if results:
            output_file = "video_scraping_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 结果已保存到: {output_file}")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
    finally:
        await scraper.cleanup()

if __name__ == "__main__":
    # 检查Playwright是否安装
    try:
        import playwright
        asyncio.run(main())
    except ImportError:
        print("❌ 请先安装Playwright:")
        print("pip install playwright")
        print("playwright install chromium")
