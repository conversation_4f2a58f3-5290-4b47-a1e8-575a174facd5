#!/usr/bin/env python3
"""
集成测试脚本
测试指纹浏览器集成系统的各个组件
"""

import os
import sys
import time
import traceback
from typing import Dict, Any

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        return False
    
    try:
        from fingerprint_browser import create_browser_manager, FingerprintBrowserManager
        print("✅ fingerprint_browser 导入成功")
    except ImportError as e:
        print(f"❌ fingerprint_browser 导入失败: {e}")
        return False
    
    try:
        from config import get_config, init_config
        print("✅ config 导入成功")
    except ImportError as e:
        print(f"❌ config 导入失败: {e}")
        return False
    
    try:
        from url import download_video
        print("✅ url 导入成功")
    except ImportError as e:
        print(f"❌ url 导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置系统"""
    print("\n🧪 测试配置系统...")
    
    try:
        from config import init_config, get_config, validate_config
        
        # 初始化配置
        if not init_config():
            print("❌ 配置初始化失败")
            return False
        
        # 获取配置
        config = get_config()
        if not config:
            print("❌ 获取配置失败")
            return False
        
        # 验证必要的配置节
        required_sections = [
            'fingerprint_browser',
            'video_download',
            'default_headers',
            'default_cookies',
            'default_params'
        ]
        
        for section in required_sections:
            if section not in config:
                print(f"❌ 缺少配置节: {section}")
                return False
            print(f"✅ 配置节 {section} 存在")
        
        # 验证指纹浏览器配置
        fb_config = config['fingerprint_browser']
        if not fb_config.get('api_key'):
            print("❌ 指纹浏览器API密钥未配置")
            return False
        
        print(f"✅ API密钥: {fb_config['api_key'][:10]}...")
        print(f"✅ 服务器: {fb_config['host']}:{fb_config['port']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        traceback.print_exc()
        return False

def test_fingerprint_browser():
    """测试指纹浏览器管理器"""
    print("\n🧪 测试指纹浏览器管理器...")
    
    try:
        from fingerprint_browser import create_browser_manager
        
        # 创建管理器
        manager = create_browser_manager()
        print("✅ 管理器创建成功")
        
        # 健康检查
        print("🔍 执行健康检查...")
        is_healthy = manager.health_check()
        
        if is_healthy:
            print("✅ 指纹浏览器服务运行正常")
            
            # 测试获取配置文件列表
            print("📋 获取配置文件列表...")
            profiles = manager.list_profiles()
            if profiles:
                profile_count = len(profiles.get('profiles', []))
                print(f"✅ 找到 {profile_count} 个配置文件")
            else:
                print("⚠️  未找到配置文件或获取失败")
            
            # 测试会话启动和停止
            print("🚀 测试会话管理...")
            session = manager.start_browser()
            if session:
                session_id = session.get('session_id')
                print(f"✅ 会话启动成功: {session_id}")
                
                # 等待一秒
                time.sleep(1)
                
                # 停止会话
                if manager.stop_browser(session_id):
                    print("✅ 会话停止成功")
                else:
                    print("❌ 会话停止失败")
            else:
                print("❌ 会话启动失败")
                return False
            
        else:
            print("⚠️  指纹浏览器服务不可用")
            print("   请确保:")
            print("   • 指纹浏览器软件已启动")
            print("   • API服务运行在 127.0.0.1:50000")
            print("   • API密钥正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 指纹浏览器测试失败: {e}")
        traceback.print_exc()
        return False

def test_download_function():
    """测试下载函数"""
    print("\n🧪 测试下载函数...")
    
    try:
        from url import download_video
        
        # 测试URL (使用一个小的测试文件)
        test_url = "https://httpbin.org/robots.txt"  # 小文件用于测试
        test_filename = "test_download.txt"
        
        print(f"📥 测试下载: {test_url}")
        
        # 普通下载测试
        print("🔧 测试普通下载...")
        success = download_video(
            url=test_url,
            filename=test_filename,
            use_fingerprint_browser=False
        )
        
        if success and os.path.exists(test_filename):
            file_size = os.path.getsize(test_filename)
            print(f"✅ 普通下载成功: {test_filename} ({file_size} bytes)")
            
            # 清理测试文件
            os.remove(test_filename)
            print("🧹 测试文件已清理")
        else:
            print("❌ 普通下载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 下载函数测试失败: {e}")
        traceback.print_exc()
        return False

def test_ffmpeg():
    """测试FFmpeg可用性"""
    print("\n🧪 测试FFmpeg...")
    
    try:
        import subprocess
        
        # 检查FFmpeg是否安装
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg 可用: {version_line}")
            return True
        else:
            print("❌ FFmpeg 不可用")
            return False
            
    except FileNotFoundError:
        print("❌ FFmpeg 未安装")
        print("   请安装FFmpeg:")
        print("   • macOS: brew install ffmpeg")
        print("   • Ubuntu: sudo apt install ffmpeg")
        print("   • Windows: 下载并添加到PATH")
        return False
    except subprocess.TimeoutExpired:
        print("❌ FFmpeg 响应超时")
        return False
    except Exception as e:
        print(f"❌ FFmpeg 测试失败: {e}")
        return False

def test_file_permissions():
    """测试文件权限"""
    print("\n🧪 测试文件权限...")
    
    try:
        # 测试当前目录写权限
        test_file = "test_permissions.tmp"
        
        with open(test_file, 'w') as f:
            f.write("test")
        
        if os.path.exists(test_file):
            os.remove(test_file)
            print("✅ 当前目录可写")
        else:
            print("❌ 当前目录不可写")
            return False
        
        # 测试下载目录
        from config import get_config
        config = get_config()
        download_dir = config['video_download']['download_dir']
        
        if not os.path.exists(download_dir):
            try:
                os.makedirs(download_dir, exist_ok=True)
                print(f"✅ 创建下载目录: {download_dir}")
            except Exception as e:
                print(f"❌ 无法创建下载目录: {e}")
                return False
        else:
            print(f"✅ 下载目录存在: {download_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件权限测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🧪 指纹浏览器集成系统测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置系统", test_config),
        ("文件权限", test_file_permissions),
        ("下载函数", test_download_function),
        ("FFmpeg", test_ffmpeg),
        ("指纹浏览器", test_fingerprint_browser),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 显示测试结果
    print(f"\n{'='*20} 测试结果 {'='*20}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置。")
        return False

def main():
    """主函数"""
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
