#!/usr/bin/env python3
"""
配置文件
管理指纹浏览器和其他服务的配置信息
"""

import os
from typing import Dict, Any

# 指纹浏览器配置
FINGERPRINT_BROWSER = {
    'api_key': '6898fca8ed6927c4bbd74fab2f6f1357',
    'host': '127.0.0.1',
    'port': 50000,
    'base_url': 'http://127.0.0.1:50000',
    'timeout': 30,  # 请求超时时间（秒）
    'max_retries': 3,  # 最大重试次数
}

# 视频下载配置
VIDEO_DOWNLOAD = {
    'chunk_size': 8192,  # 下载块大小
    'timeout': 60,  # 下载超时时间（秒）
    'max_retries': 3,  # 最大重试次数
    'default_filename': 'downloaded_video.mp4',
    'download_dir': './downloads',  # 默认下载目录
}

# 日志配置
LOGGING = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'app.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# 代理配置
PROXY = {
    'enabled': False,
    'http': None,  # 'http://proxy.example.com:8080'
    'https': None,  # 'https://proxy.example.com:8080'
    'auth': None,  # ('username', 'password')
}

# 用户代理配置
USER_AGENTS = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
]

# 请求头配置
DEFAULT_HEADERS = {
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Connection': 'keep-alive',
    'Sec-Fetch-Dest': 'video',
    'Sec-Fetch-Mode': 'no-cors',
    'Sec-Fetch-Site': 'same-site',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
}

# Cookie配置
DEFAULT_COOKIES = {
    'language': 'en',
    '_gid': 'GA1.2.**********.**********',
    '__gads': 'ID=edc7ae4b62506779:T=**********:RT=**********:S=ALNI_MZHIJAyuQ10wjEpmrBDAEjlS-Mm0g',
    '__gpi': 'UID=0000125b55d9c257:T=**********:RT=**********:S=ALNI_MaHZ0I6OoEfpEfWCLQnBw05txiNGQ',
    '__eoi': 'ID=f71b8a19c2b2ca3d:T=**********:RT=**********:S=AA-AfjZKHIfNVSxtwmHd2Zog4mlk',
    'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************.jtNlQi-rNkMhyJ1gkGipLpfo-zq2DM0B8Gv8mZiXyM0',
    'FCNEC': '%5B%5B%22AKsRol8HBJ2XWgeEnL6EfQ46pzHfnu3tZ1H5SLBZARgq7VT3n9DOaUpfSV5ZCfia1vKrnOBmnXT2hUZaRy-KCAF6Qf2op7efEOTlvCb0kru4U6ruVGICJDGW3wolXJrGECo3iDZiO_xKsMe9NcSKOlshWklNO3Uw_w%3D%3D%22%5D%5D',
    '_ga_J53YKEV8JL': 'GS2.1.s**********$o1$g1$t1754884562$j60$l0$h0',
    '_ga': 'GA1.2.*********.**********',
    '_gat_gtag_UA_239990267_1': '1',
}

# 请求参数配置
DEFAULT_PARAMS = {
    'auth_key': '**********-689969d3d27cc-0-de13b8ff54c7c0fdbebba1aa4d3e6f63',
}

# FFmpeg配置
FFMPEG = {
    'binary_path': 'ffmpeg',  # FFmpeg可执行文件路径
    'preset': 'medium',  # 编码预设: ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow
    'crf': 18,  # 恒定质量因子 (0-51, 越小质量越好)
    'audio_codec': 'copy',  # 音频编解码器
    'video_codec': 'libx264',  # 视频编解码器
    'output_format': 'mp4',  # 输出格式
}

# 水印去除配置
WATERMARK_REMOVAL = {
    'methods': [
        'delogo',
        'advanced_delogo',
        'temporal_repair',
        'inpaint',
        'median_filter',
        'edge_fill',
        'blur'
    ],
    'watermark_positions': {
        'top_right': {
            'x_ratio': 0.82,  # 距离右边的比例
            'y_ratio': 0.05,  # 距离顶部的比例
            'width_ratio': 0.18,  # 宽度比例
            'height_ratio': 0.10,  # 高度比例
        },
        'bottom_center': {
            'x_ratio': 0.0,
            'y_ratio': 0.85,
            'width_ratio': 1.0,
            'height_ratio': 0.15,
        }
    }
}

def get_config(section: str = None) -> Dict[str, Any]:
    """
    获取配置信息
    
    Args:
        section: 配置节名称，如果为None则返回所有配置
        
    Returns:
        配置字典
    """
    all_config = {
        'fingerprint_browser': FINGERPRINT_BROWSER,
        'video_download': VIDEO_DOWNLOAD,
        'logging': LOGGING,
        'proxy': PROXY,
        'user_agents': USER_AGENTS,
        'default_headers': DEFAULT_HEADERS,
        'default_cookies': DEFAULT_COOKIES,
        'default_params': DEFAULT_PARAMS,
        'ffmpeg': FFMPEG,
        'watermark_removal': WATERMARK_REMOVAL,
    }
    
    if section:
        return all_config.get(section, {})
    return all_config

def update_config(section: str, key: str, value: Any) -> bool:
    """
    更新配置项
    
    Args:
        section: 配置节名称
        key: 配置键
        value: 新值
        
    Returns:
        是否更新成功
    """
    try:
        config_map = {
            'fingerprint_browser': FINGERPRINT_BROWSER,
            'video_download': VIDEO_DOWNLOAD,
            'logging': LOGGING,
            'proxy': PROXY,
            'ffmpeg': FFMPEG,
            'watermark_removal': WATERMARK_REMOVAL,
        }
        
        if section in config_map:
            config_map[section][key] = value
            return True
        return False
    except Exception:
        return False

def load_config_from_env():
    """从环境变量加载配置"""
    # 指纹浏览器配置
    if os.getenv('FINGERPRINT_API_KEY'):
        FINGERPRINT_BROWSER['api_key'] = os.getenv('FINGERPRINT_API_KEY')
    if os.getenv('FINGERPRINT_HOST'):
        FINGERPRINT_BROWSER['host'] = os.getenv('FINGERPRINT_HOST')
    if os.getenv('FINGERPRINT_PORT'):
        try:
            FINGERPRINT_BROWSER['port'] = int(os.getenv('FINGERPRINT_PORT'))
            FINGERPRINT_BROWSER['base_url'] = f"http://{FINGERPRINT_BROWSER['host']}:{FINGERPRINT_BROWSER['port']}"
        except ValueError:
            pass
    
    # 代理配置
    if os.getenv('HTTP_PROXY'):
        PROXY['http'] = os.getenv('HTTP_PROXY')
        PROXY['enabled'] = True
    if os.getenv('HTTPS_PROXY'):
        PROXY['https'] = os.getenv('HTTPS_PROXY')
        PROXY['enabled'] = True
    
    # 下载目录
    if os.getenv('DOWNLOAD_DIR'):
        VIDEO_DOWNLOAD['download_dir'] = os.getenv('DOWNLOAD_DIR')

def create_download_dir():
    """创建下载目录"""
    download_dir = VIDEO_DOWNLOAD['download_dir']
    if not os.path.exists(download_dir):
        try:
            os.makedirs(download_dir, exist_ok=True)
            print(f"✅ 创建下载目录: {download_dir}")
        except Exception as e:
            print(f"❌ 创建下载目录失败: {e}")
            VIDEO_DOWNLOAD['download_dir'] = './'

def validate_config() -> bool:
    """验证配置的有效性"""
    errors = []
    
    # 验证指纹浏览器配置
    if not FINGERPRINT_BROWSER['api_key']:
        errors.append("指纹浏览器API密钥未设置")
    
    if not (1 <= FINGERPRINT_BROWSER['port'] <= 65535):
        errors.append("指纹浏览器端口无效")
    
    # 验证下载配置
    if VIDEO_DOWNLOAD['chunk_size'] <= 0:
        errors.append("下载块大小必须大于0")
    
    if VIDEO_DOWNLOAD['timeout'] <= 0:
        errors.append("下载超时时间必须大于0")
    
    # 验证FFmpeg配置
    if FFMPEG['crf'] < 0 or FFMPEG['crf'] > 51:
        errors.append("FFmpeg CRF值必须在0-51之间")
    
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   • {error}")
        return False
    
    print("✅ 配置验证通过")
    return True

# 初始化配置
def init_config():
    """初始化配置"""
    print("🔧 初始化配置...")
    
    # 从环境变量加载配置
    load_config_from_env()
    
    # 创建下载目录
    create_download_dir()
    
    # 验证配置
    return validate_config()

if __name__ == "__main__":
    # 测试配置
    print("🧪 配置测试")
    print("=" * 50)
    
    if init_config():
        print("\n📋 当前配置:")
        config = get_config()
        for section, values in config.items():
            print(f"\n[{section.upper()}]")
            for key, value in values.items():
                if isinstance(value, dict):
                    print(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"    {sub_key}: {sub_value}")
                elif isinstance(value, list):
                    print(f"  {key}: [{len(value)} items]")
                else:
                    # 隐藏敏感信息
                    if 'key' in key.lower() or 'token' in key.lower():
                        display_value = f"{str(value)[:10]}..." if len(str(value)) > 10 else str(value)
                    else:
                        display_value = value
                    print(f"  {key}: {display_value}")
    
    print("\n🎉 配置测试完成")
