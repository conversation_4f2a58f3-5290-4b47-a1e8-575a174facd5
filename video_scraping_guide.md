# 🎬 Playwright + 指纹浏览器视频抓取指南

本指南介绍如何使用Playwright结合指纹浏览器来自动抓取视频页面的真实视频链接。

## 📋 功能特性

- ✅ **指纹浏览器集成**: 使用Roxy Browser进行匿名访问
- ✅ **智能链接提取**: 自动识别多种视频格式 (MP4, M3U8, TS等)
- ✅ **网络监听**: 实时捕获视频资源请求
- ✅ **页面解析**: 从HTML源码中提取视频URL
- ✅ **JavaScript执行**: 获取动态加载的视频链接
- ✅ **自动下载**: 支持直接下载抓取到的视频

## 🚀 快速开始

### 1. 基础使用

```bash
# 运行主程序
python url.py

# 选择功能 3 - 抓取视频页面链接
# 输入视频页面URL
# 系统会自动抓取并显示所有找到的视频链接
```

### 2. 使用简化版抓取器

```bash
# 直接运行简化版抓取器
python simple_video_scraper.py

# 输入视频页面URL
# 选择是否下载找到的视频
```

### 3. 使用完整版Playwright抓取器

```bash
# 需要先安装Playwright
pip install playwright
playwright install chromium

# 运行完整版抓取器
python playwright_video_scraper.py
```

## 🔧 配置说明

### Roxy Browser配置

确保Roxy Browser服务正在运行：
- 服务地址: `http://127.0.0.1:50000`
- API Token: `6898fca8ed6927c4bbd74fab2f6f1357`
- 工作空间: "xirichuyi's Workspace"
- 浏览器窗口: "flexTv"

### 支持的视频格式

- **MP4**: 标准视频格式
- **M3U8**: HLS流媒体格式
- **TS**: 视频片段格式
- **FLV**: Flash视频格式
- **AVI**: 音视频交错格式
- **MOV**: QuickTime格式
- **WMV**: Windows媒体格式
- **WEBM**: Web视频格式

## 📖 使用示例

### 示例1: 抓取FlexTV视频

```python
from simple_video_scraper import SimpleVideoScraper

# 创建抓取器
scraper = SimpleVideoScraper()

# 设置指纹浏览器
scraper.setup_session_with_fingerprint()

# 抓取视频页面
result = scraper.scrape_video_page("https://flextv.cc/video/example")

if result['success']:
    print(f"找到 {result['count']} 个视频链接")
    for video in result['video_urls']:
        print(f"URL: {video['url']}")
        print(f"类型: {video['type']}")
        print(f"质量: {video['quality']}")
```

### 示例2: 使用Playwright进行深度抓取

```python
from flextv_scraper import FlexTVScraper

async def main():
    scraper = FlexTVScraper()
    
    # 抓取视频
    result = await scraper.scrape_flextv_video("https://flextv.cc/video/example")
    
    if result['success']:
        print(f"找到 {result['count']} 个视频链接")
        for video in result['video_urls']:
            print(f"视频源: {video['url']}")

# 运行异步函数
import asyncio
asyncio.run(main())
```

## 🎯 抓取策略

### 1. 网络监听策略
- 监听所有网络请求
- 过滤视频相关的Content-Type
- 捕获.mp4、.m3u8等视频文件URL

### 2. 页面解析策略
- 使用正则表达式匹配视频URL
- 解析video标签和source标签
- 提取JavaScript中的视频配置

### 3. 交互策略
- 自动查找并点击播放按钮
- 等待视频加载完成
- 处理动态内容加载

## 🔍 支持的网站类型

### 通用视频网站
- 支持标准HTML5视频播放器
- 支持HLS流媒体播放器
- 支持自定义视频播放器

### FlexTV特定优化
- 针对FlexTV网站结构优化
- 支持FlexTV的视频URL格式
- 自动处理FlexTV的认证机制

## ⚠️ 注意事项

### 1. 合法使用
- 仅用于个人学习和研究目的
- 遵守网站的使用条款和版权规定
- 不要用于商业用途或大规模抓取

### 2. 技术限制
- 某些网站可能有反爬虫机制
- 动态加载的内容可能需要额外等待时间
- 加密的视频流可能无法直接下载

### 3. 性能考虑
- 指纹浏览器会消耗更多系统资源
- 网络监听可能影响抓取速度
- 建议适当设置延迟和超时时间

## 🛠️ 故障排除

### 常见问题

1. **Roxy Browser连接失败**
   ```
   解决方案: 确保Roxy Browser服务正在运行，检查API Token是否正确
   ```

2. **未找到视频链接**
   ```
   解决方案: 尝试增加等待时间，检查网站是否需要登录或特殊权限
   ```

3. **Playwright安装失败**
   ```
   解决方案: 使用简化版抓取器，或手动安装Playwright浏览器
   ```

### 调试模式

启用详细日志输出：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 相关文件

- `url.py` - 主程序入口，集成所有功能
- `simple_video_scraper.py` - 简化版抓取器，使用requests
- `playwright_video_scraper.py` - 完整版抓取器，使用Playwright
- `flextv_scraper.py` - 专门针对FlexTV的抓取器
- `fingerprint_browser.py` - 指纹浏览器管理器
- `config.py` - 配置管理

## 🎉 总结

通过结合指纹浏览器和Playwright技术，我们实现了一个强大的视频链接抓取工具。该工具能够：

1. **绕过反爬虫机制**: 使用真实浏览器环境和指纹伪装
2. **处理动态内容**: 支持JavaScript渲染的页面
3. **多格式支持**: 识别各种视频格式和流媒体协议
4. **自动化操作**: 模拟用户点击和交互行为
5. **灵活配置**: 支持多种抓取策略和参数调整

这个工具为视频内容的获取和分析提供了强大的技术支持，同时保持了良好的用户体验和系统稳定性。
