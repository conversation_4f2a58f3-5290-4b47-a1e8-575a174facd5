#!/usr/bin/env python3
"""
专门针对FlexTV的视频链接抓取器
使用Playwright + 指纹浏览器自动获取视频真实下载链接
"""

import asyncio
import json
import re
from typing import Optional, List, Dict
from playwright.async_api import async_playwright, <PERSON>rowser, <PERSON>
from fingerprint_browser import create_browser_manager

class FlexTVScraper:
    """FlexTV视频抓取器"""
    
    def __init__(self):
        self.browser_manager = create_browser_manager()
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.session_data = None
        self.video_urls = []
        
    async def start_browser(self) -> bool:
        """启动指纹浏览器并连接Playwright"""
        print("🚀 启动指纹浏览器...")
        
        # 启动Roxy Browser会话
        if not self.browser_manager.health_check():
            print("❌ Roxy Browser服务不可用")
            return False
        
        self.session_data = self.browser_manager.start_browser()
        if not self.session_data:
            print("❌ 无法启动浏览器会话")
            return False
        
        # 连接Playwright
        try:
            playwright = await async_playwright().start()
            
            # 解析调试端口
            ws_url = self.session_data.get('ws', '')
            port_match = re.search(r':(\d+)/', ws_url)
            if not port_match:
                print("❌ 无法解析调试端口")
                return False
                
            debug_port = port_match.group(1)
            cdp_url = f"http://127.0.0.1:{debug_port}"
            
            print(f"🔗 连接到浏览器: {cdp_url}")
            self.browser = await playwright.chromium.connect_over_cdp(cdp_url)
            
            # 获取或创建页面
            contexts = self.browser.contexts
            if contexts and contexts[0].pages:
                self.page = contexts[0].pages[0]
            else:
                context = await self.browser.new_context()
                self.page = await context.new_page()
            
            # 设置网络监听
            self.page.on('response', self._handle_response)
            
            print("✅ 浏览器连接成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器连接失败: {e}")
            return False
    
    def _handle_response(self, response):
        """处理网络响应，捕获视频URL"""
        url = response.url
        content_type = response.headers.get('content-type', '')
        
        # 检查是否是视频文件
        if (any(ext in url.lower() for ext in ['.mp4', '.m3u8', '.ts']) or
            'video' in content_type.lower() or
            'application/vnd.apple.mpegurl' in content_type.lower()):
            
            self.video_urls.append({
                'url': url,
                'content_type': content_type,
                'status': response.status,
                'size': response.headers.get('content-length', 'unknown')
            })
            print(f"📹 捕获视频URL: {url}")
    
    async def navigate_and_extract(self, page_url: str) -> Dict:
        """导航到页面并提取视频信息"""
        if not self.page:
            return {'success': False, 'error': '页面未初始化'}
        
        try:
            print(f"🌐 导航到: {page_url}")
            
            # 清空之前的视频URL
            self.video_urls = []
            
            # 导航到页面
            await self.page.goto(page_url, wait_until='networkidle', timeout=30000)
            await asyncio.sleep(3)
            
            # 尝试查找并点击播放相关元素
            play_selectors = [
                'video',  # 直接的video标签
                '.play-button',
                '.video-play-btn', 
                '[data-play]',
                'button[aria-label*="play"]',
                '.play-icon',
                '.player-play-btn',
                '.video-player',
                '#video-player'
            ]
            
            played = False
            for selector in play_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        print(f"🎯 找到播放元素: {selector}")
                        await element.click()
                        await asyncio.sleep(2)
                        played = True
                        break
                except:
                    continue
            
            if not played:
                print("⚠️  未找到播放按钮，尝试其他方法...")
            
            # 等待视频加载
            await asyncio.sleep(5)
            
            # 尝试从页面源码提取视频URL
            page_content = await self.page.content()
            self._extract_from_page_source(page_content)
            
            # 执行JavaScript获取视频元素信息
            try:
                video_info = await self.page.evaluate("""
                    () => {
                        const videos = document.querySelectorAll('video');
                        const sources = [];
                        
                        videos.forEach(video => {
                            if (video.src) {
                                sources.push({
                                    src: video.src,
                                    type: 'video',
                                    element: 'video'
                                });
                            }
                            
                            // 检查source标签
                            const sourceTags = video.querySelectorAll('source');
                            sourceTags.forEach(source => {
                                if (source.src) {
                                    sources.push({
                                        src: source.src,
                                        type: source.type || 'video',
                                        element: 'source'
                                    });
                                }
                            });
                        });
                        
                        return sources;
                    }
                """)
                
                for info in video_info:
                    if info['src'] and info['src'].startswith('http'):
                        self.video_urls.append({
                            'url': info['src'],
                            'content_type': info['type'],
                            'status': 200,
                            'source': 'javascript'
                        })
                        print(f"📹 JS提取视频URL: {info['src']}")
                        
            except Exception as e:
                print(f"⚠️  JavaScript提取失败: {e}")
            
            # 整理结果
            unique_urls = []
            seen = set()
            
            for video in self.video_urls:
                url = video['url']
                if url not in seen and url.startswith('http'):
                    seen.add(url)
                    unique_urls.append(video)
            
            return {
                'success': True,
                'page_url': page_url,
                'video_urls': unique_urls,
                'count': len(unique_urls)
            }
            
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _extract_from_page_source(self, content: str):
        """从页面源码中提取视频URL"""
        patterns = [
            r'"(https?://[^"]*\.mp4[^"]*)"',
            r"'(https?://[^']*\.mp4[^']*)'",
            r'"(https?://[^"]*\.m3u8[^"]*)"',
            r"'(https?://[^']*\.m3u8[^']*)'",
            r'src="([^"]*\.mp4[^"]*)"',
            r"src='([^']*\.mp4[^']*)'",
            r'url:\s*["\']([^"\']*\.(mp4|m3u8)[^"\']*)["\']',
            r'video_url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'source["\']?\s*[:=]\s*["\']([^"\']*\.(mp4|m3u8)[^"\']*)["\']'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                url = match if isinstance(match, str) else match[0]
                if url and url.startswith('http'):
                    self.video_urls.append({
                        'url': url,
                        'content_type': 'video/mp4',
                        'status': 200,
                        'source': 'page_source'
                    })
                    print(f"📹 源码提取视频URL: {url}")
    
    async def scrape_flextv_video(self, video_page_url: str) -> Dict:
        """抓取FlexTV视频页面的视频链接"""
        try:
            # 启动浏览器
            if not await self.start_browser():
                return {'success': False, 'error': '浏览器启动失败'}
            
            # 提取视频信息
            result = await self.navigate_and_extract(video_page_url)
            
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                await self.browser.close()
            
            if self.session_data and self.browser_manager:
                session_id = self.session_data.get('session_id')
                if session_id:
                    self.browser_manager.stop_browser(session_id)
                    
        except Exception as e:
            print(f"⚠️  清理资源时出错: {e}")

async def main():
    """主函数"""
    print("🎬 FlexTV视频链接抓取器")
    print("=" * 50)
    
    # 获取用户输入
    video_url = input("请输入FlexTV视频页面URL: ").strip()
    if not video_url:
        # 使用示例URL
        video_url = "https://flextv.cc/video/example"
        print(f"使用示例URL: {video_url}")
    
    # 创建抓取器
    scraper = FlexTVScraper()
    
    try:
        print(f"\n🚀 开始抓取视频: {video_url}")
        result = await scraper.scrape_flextv_video(video_url)
        
        if result['success']:
            print(f"\n✅ 抓取成功！找到 {result['count']} 个视频链接:")
            print("=" * 50)
            
            for i, video in enumerate(result['video_urls'], 1):
                print(f"\n{i}. 视频链接:")
                print(f"   URL: {video['url']}")
                print(f"   类型: {video.get('content_type', 'unknown')}")
                print(f"   大小: {video.get('size', 'unknown')}")
                print(f"   来源: {video.get('source', 'network')}")
            
            # 保存结果
            output_file = "flextv_video_links.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n💾 结果已保存到: {output_file}")
            
            # 如果找到视频链接，询问是否下载
            if result['video_urls']:
                download = input("\n是否使用找到的链接下载视频? (y/N): ").strip().lower()
                if download == 'y':
                    # 选择最佳视频链接（通常是第一个mp4链接）
                    best_video = None
                    for video in result['video_urls']:
                        if '.mp4' in video['url'].lower():
                            best_video = video
                            break
                    
                    if not best_video:
                        best_video = result['video_urls'][0]
                    
                    print(f"\n📥 开始下载: {best_video['url']}")
                    
                    # 调用现有的下载函数
                    from url import download_video
                    success = download_video(
                        url=best_video['url'],
                        filename="flextv_scraped_video.mp4",
                        use_fingerprint_browser=True
                    )
                    
                    if success:
                        print("🎉 视频下载完成！")
                    else:
                        print("💥 视频下载失败！")
        else:
            print(f"\n❌ 抓取失败: {result.get('error', '未知错误')}")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    # 检查依赖
    try:
        import playwright
        asyncio.run(main())
    except ImportError:
        print("❌ 请先安装Playwright:")
        print("pip install playwright")
        print("playwright install chromium")
