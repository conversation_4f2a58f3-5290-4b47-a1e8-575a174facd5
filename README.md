# 指纹浏览器集成视频下载工具

这是一个集成了指纹浏览器功能的视频下载和处理工具集，支持通过指纹浏览器API进行匿名下载和视频水印去除。

## 🚀 功能特性

- **指纹浏览器集成**: 支持通过指纹浏览器API进行匿名下载
- **视频下载**: 支持多种视频格式的下载
- **水印去除**: 专业的FFmpeg水印去除工具
- **配置管理**: 灵活的配置系统
- **会话管理**: 自动管理浏览器会话生命周期

## 📋 系统要求

- Python 3.7+
- FFmpeg (用于视频处理)
- 指纹浏览器软件 (如AdsPower、VMLogin等)

## 🔧 安装配置

### 1. 安装Python依赖

```bash
pip install requests
```

### 2. 安装FFmpeg

**macOS:**
```bash
brew install ffmpeg
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install ffmpeg
```

**Windows:**
下载FFmpeg并添加到系统PATH

### 3. 配置指纹浏览器

1. 启动您的指纹浏览器软件
2. 确保API服务运行在 `127.0.0.1:50000`
3. 获取API密钥: `6898fca8ed6927c4bbd74fab2f6f1357`

## 📁 文件结构

```
temp3/
├── url.py                      # 主要的视频下载脚本
├── fingerprint_browser.py      # 指纹浏览器管理模块
├── config.py                   # 配置管理模块
├── example_usage.py            # 使用示例脚本
├── flex_tv_watermark_remover.py # 水印去除工具
└── README.md                   # 说明文档
```

## 🎯 使用方法

### 基础视频下载

```bash
python url.py
```

运行后会提示选择下载方式：
1. 普通下载 (默认)
2. 使用指纹浏览器下载

### 指纹浏览器功能测试

```bash
python example_usage.py
```

这个脚本提供了完整的指纹浏览器功能演示：
- 浏览器会话管理
- 配置文件操作
- 指纹浏览器下载

### 水印去除

```bash
python flex_tv_watermark_remover.py
```

支持多种水印去除方法：
- Delogo智能修复
- 高级Delogo
- 时间域修复
- 智能修复算法
- 中值滤波
- 边缘填充
- 区域模糊

## ⚙️ 配置说明

### 指纹浏览器配置

在 `config.py` 中修改以下配置：

```python
FINGERPRINT_BROWSER = {
    'api_key': '6898fca8ed6927c4bbd74fab2f6f1357',  # 您的API密钥
    'host': '127.0.0.1',                            # 服务器地址
    'port': 50000,                                  # 服务器端口
    'timeout': 30,                                  # 请求超时时间
    'max_retries': 3,                              # 最大重试次数
}
```

### 环境变量配置

您也可以通过环境变量设置配置：

```bash
export FINGERPRINT_API_KEY="your_api_key_here"
export FINGERPRINT_HOST="127.0.0.1"
export FINGERPRINT_PORT="50000"
export DOWNLOAD_DIR="./downloads"
```

## 🔍 API使用示例

### 基础使用

```python
from fingerprint_browser import create_browser_manager
from url import download_video

# 创建浏览器管理器
manager = create_browser_manager()

# 健康检查
if manager.health_check():
    # 使用指纹浏览器下载
    success = download_video(
        url="https://example.com/video.mp4",
        filename="my_video.mp4",
        use_fingerprint_browser=True,
        profile_id="your_profile_id"  # 可选
    )
```

### 高级会话管理

```python
from fingerprint_browser import create_browser_manager

manager = create_browser_manager()

# 启动会话
session = manager.start_browser(profile_id="profile_123")
if session:
    session_id = session['session_id']
    
    # 执行您的操作...
    
    # 停止会话
    manager.stop_browser(session_id)
```

## 🛠️ 故障排除

### 常见问题

1. **指纹浏览器连接失败**
   - 确保指纹浏览器软件已启动
   - 检查API服务是否运行在正确端口
   - 验证API密钥是否正确

2. **下载失败**
   - 检查网络连接
   - 验证视频URL是否有效
   - 确认cookies和认证信息是否正确

3. **FFmpeg错误**
   - 确保FFmpeg已正确安装
   - 检查视频文件是否损坏
   - 验证输出路径是否可写

### 调试模式

启用详细日志输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 性能优化

### 下载优化

- 调整 `chunk_size` 以优化下载速度
- 使用多线程下载大文件
- 配置合适的超时时间

### 内存优化

- 使用流式下载避免内存溢出
- 及时关闭浏览器会话
- 定期清理临时文件

## 🔒 安全注意事项

1. **API密钥安全**
   - 不要在代码中硬编码API密钥
   - 使用环境变量或配置文件
   - 定期更换API密钥

2. **网络安全**
   - 使用HTTPS连接
   - 验证SSL证书
   - 配置适当的代理设置

3. **文件安全**
   - 验证下载文件的完整性
   - 扫描下载内容的安全性
   - 限制文件访问权限

## 📝 更新日志

### v1.0.0 (2025-01-17)
- 初始版本发布
- 集成指纹浏览器API
- 支持视频下载和水印去除
- 完整的配置管理系统

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证。

## 📞 支持

如果您遇到问题或需要帮助，请：

1. 查看本README文档
2. 检查常见问题部分
3. 提交Issue描述问题
4. 联系技术支持

---

**注意**: 请确保您的使用符合相关法律法规和服务条款。
