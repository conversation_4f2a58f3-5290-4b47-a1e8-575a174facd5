import requests
import os
from urllib.parse import urlparse
import time
from fingerprint_browser import create_browser_manager
from config import get_config, init_config

# 初始化配置
init_config()

# 获取配置
config = get_config()
cookies = config['default_cookies']

headers = config['default_headers'].copy()
headers['Range'] = 'bytes=0-'  # 添加Range头用于视频下载

params = config['default_params']

def download_video(url, filename=None, use_fingerprint_browser=False, profile_id=None):
    """
    下载视频文件到本地

    Args:
        url: 视频URL
        filename: 保存的文件名
        use_fingerprint_browser: 是否使用指纹浏览器
        profile_id: 指纹浏览器配置文件ID
    """
    if filename is None:
        # 从URL中提取文件名
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        if not filename or not filename.endswith('.mp4'):
            filename = 'video.mp4'

    print(f"开始下载视频: {filename}")
    print(f"视频URL: {url}")

    # 指纹浏览器会话管理
    browser_manager = None
    browser_session = None
    if use_fingerprint_browser:
        print("🔧 启动指纹浏览器会话...")
        browser_manager = create_browser_manager()
        if browser_manager.health_check():
            browser_session = browser_manager.start_browser(profile_id)
            if not browser_session:
                print("⚠️  指纹浏览器启动失败，使用默认方式下载")
        else:
            print("⚠️  指纹浏览器服务不可用，使用默认方式下载")

    try:
        # 发送请求
        response = requests.get(
            url,
            params=params,
            cookies=cookies,
            headers=headers,
            stream=True  # 使用流式下载，适合大文件
        )

        # 检查响应状态
        response.raise_for_status()

        # 获取文件大小
        total_size = int(response.headers.get('content-length', 0))

        # 下载文件
        downloaded_size = 0
        chunk_size = config['video_download']['chunk_size']
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)

                    # 显示下载进度
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
                    else:
                        print(f"\r已下载: {downloaded_size} bytes", end='')

        print(f"\n✅ 视频下载完成: {filename}")
        print(f"文件大小: {downloaded_size} bytes")

        # 关闭指纹浏览器会话
        if browser_manager and browser_session and browser_session.get('session_id'):
            browser_manager.stop_browser(browser_session['session_id'])

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ 下载失败: {e}")
        # 确保关闭浏览器会话
        if browser_manager and browser_session and browser_session.get('session_id'):
            browser_manager.stop_browser(browser_session['session_id'])
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        # 确保关闭浏览器会话
        if browser_manager and browser_session and browser_session.get('session_id'):
            browser_manager.stop_browser(browser_session['session_id'])
        return False

# 视频URL
video_url = 'https://resources-sgp-auth.flextv.cc/wz/mp4/a/b/abd9961b60a8c28d3a4b19dec28a2335/b5d165f76cd08855282f1823ad44b405/1080p-max.mp4'

# 执行下载
if __name__ == "__main__":
    print("🎬 视频下载工具")
    print("=" * 50)

    # 选择下载方式
    print("请选择下载方式:")
    print("1. 普通下载 (默认)")
    print("2. 使用指纹浏览器下载")

    choice = input("请输入选择 (1-2, 默认为1): ").strip()

    if choice == "2":
        # 使用指纹浏览器下载
        profile_id = input("请输入配置文件ID (可选，直接回车跳过): ").strip()
        profile_id = profile_id if profile_id else None

        print(f"🔧 使用指纹浏览器下载...")
        print(f"   API密钥: {'6898fca8ed6927c4bbd74fab2f6f1357'[:10]}...")
        print(f"   服务器: http://127.0.0.1:50000")
        if profile_id:
            print(f"   配置文件: {profile_id}")

        success = download_video(video_url, "downloaded_video_fingerprint.mp4",
                               use_fingerprint_browser=True, profile_id=profile_id)
    else:
        # 普通下载
        print("🔧 使用普通方式下载...")
        success = download_video(video_url, "downloaded_video.mp4")

    if success:
        print("🎉 下载任务完成！")
    else:
        print("💥 下载任务失败！")