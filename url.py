import requests
import os
from urllib.parse import urlparse
import time
from fingerprint_browser import create_browser_manager
from config import get_config, init_config

# 初始化配置
init_config()

# 获取配置
config = get_config()
cookies = config['default_cookies']

headers = config['default_headers'].copy()
headers['Range'] = 'bytes=0-'  # 添加Range头用于视频下载

params = config['default_params']

def download_video(url, filename=None, use_fingerprint_browser=False, profile_id=None):
    """
    下载视频文件到本地

    Args:
        url: 视频URL
        filename: 保存的文件名
        use_fingerprint_browser: 是否使用指纹浏览器
        profile_id: 指纹浏览器配置文件ID
    """
    if filename is None:
        # 从URL中提取文件名
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        if not filename or not filename.endswith('.mp4'):
            filename = 'video.mp4'

    print(f"开始下载视频: {filename}")
    print(f"视频URL: {url}")

    # 指纹浏览器会话管理
    browser_manager = None
    browser_session = None
    if use_fingerprint_browser:
        print("🔧 启动指纹浏览器会话...")
        browser_manager = create_browser_manager()
        if browser_manager.health_check():
            browser_session = browser_manager.start_browser(profile_id)
            if not browser_session:
                print("⚠️  指纹浏览器启动失败，使用默认方式下载")
        else:
            print("⚠️  指纹浏览器服务不可用，使用默认方式下载")

    try:
        # 发送请求
        response = requests.get(
            url,
            params=params,
            cookies=cookies,
            headers=headers,
            stream=True  # 使用流式下载，适合大文件
        )

        # 检查响应状态
        response.raise_for_status()

        # 获取文件大小
        total_size = int(response.headers.get('content-length', 0))

        # 下载文件
        downloaded_size = 0
        chunk_size = config['video_download']['chunk_size']
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)

                    # 显示下载进度
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
                    else:
                        print(f"\r已下载: {downloaded_size} bytes", end='')

        print(f"\n✅ 视频下载完成: {filename}")
        print(f"文件大小: {downloaded_size} bytes")

        # 关闭指纹浏览器会话
        if browser_manager and browser_session and browser_session.get('session_id'):
            browser_manager.stop_browser(browser_session['session_id'])

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ 下载失败: {e}")
        # 确保关闭浏览器会话
        if browser_manager and browser_session and browser_session.get('session_id'):
            browser_manager.stop_browser(browser_session['session_id'])
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        # 确保关闭浏览器会话
        if browser_manager and browser_session and browser_session.get('session_id'):
            browser_manager.stop_browser(browser_session['session_id'])
        return False

def scrape_video_links(page_url: str) -> dict:
    """抓取视频页面的视频链接"""
    try:
        from simple_video_scraper import SimpleVideoScraper
        scraper = SimpleVideoScraper()
        return scraper.scrape_video_page(page_url)
    except ImportError:
        print("❌ 视频抓取功能不可用，请确保已安装相关依赖")
        return {'success': False, 'error': '缺少依赖'}
    except Exception as e:
        print(f"❌ 视频抓取失败: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    print("🎬 视频下载工具")
    print("=" * 50)

    while True:
        print("\n请选择功能:")
        print("1. 普通下载")
        print("2. 使用指纹浏览器下载")
        print("3. 抓取视频页面链接")
        print("4. 退出")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == '4':
            print("👋 再见!")
            break
        elif choice == '3':
            # 抓取视频链接
            page_url = input("\n请输入视频页面URL: ").strip()
            if not page_url:
                print("❌ URL不能为空")
                continue

            print(f"\n🔍 正在抓取视频链接...")
            result = scrape_video_links(page_url)

            if result['success']:
                print(f"\n✅ 抓取成功！找到 {result['count']} 个视频链接:")
                print("=" * 50)

                for i, video in enumerate(result['video_urls'], 1):
                    print(f"\n{i}. 视频链接:")
                    print(f"   URL: {video['url']}")
                    print(f"   类型: {video['type']}")
                    print(f"   质量: {video['quality']}")

                if result['video_urls']:
                    download_choice = input("\n是否下载其中一个视频? (y/N): ").strip().lower()
                    if download_choice == 'y':
                        try:
                            video_index = int(input(f"请选择要下载的视频 (1-{len(result['video_urls'])}): ")) - 1
                            if 0 <= video_index < len(result['video_urls']):
                                selected_video = result['video_urls'][video_index]
                                filename = f"scraped_video_{video_index+1}.{selected_video['type']}"

                                print(f"\n📥 开始下载: {selected_video['url']}")
                                success = download_video(
                                    url=selected_video['url'],
                                    filename=filename,
                                    use_fingerprint_browser=True
                                )

                                if success:
                                    print("🎉 视频下载完成！")
                                else:
                                    print("💥 视频下载失败！")
                            else:
                                print("❌ 无效的选择")
                        except ValueError:
                            print("❌ 请输入有效的数字")
            else:
                print(f"\n❌ 抓取失败: {result.get('error', '未知错误')}")

        elif choice in ['1', '2']:
            use_fingerprint = (choice == '2')

            # 获取用户输入
            url = input("\n请输入视频URL: ").strip()
            if not url:
                print("❌ URL不能为空")
                continue

            filename = input("请输入保存文件名 (留空使用默认名): ").strip()
            if not filename:
                filename = "downloaded_video.mp4"

            # 开始下载
            print(f"\n� 开始下载...")
            print(f"   URL: {url}")
            print(f"   文件名: {filename}")
            print(f"   模式: {'指纹浏览器' if use_fingerprint else '普通'}")

            success = download_video(url, filename, use_fingerprint)

            if success:
                print("🎉 下载完成!")
            else:
                print("💥 下载失败!")
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()