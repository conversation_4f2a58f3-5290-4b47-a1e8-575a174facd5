#!/usr/bin/env python3
"""
指纹浏览器API管理模块
用于与本地指纹浏览器服务进行交互
"""

import requests
import json
import time
from typing import Optional, Dict, Any

class FingerprintBrowserManager:
    """指纹浏览器管理器"""
    
    def __init__(self, api_key: str, host: str = '127.0.0.1', port: int = 50000):
        """
        初始化指纹浏览器管理器
        
        Args:
            api_key: API密钥
            host: 服务器主机地址
            port: 服务器端口
        """
        self.api_key = api_key
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.active_sessions = {}
        
    def _make_request(self, endpoint: str, method: str = 'POST', data: Optional[Dict] = None) -> Optional[Dict]:
        """
        发送API请求
        
        Args:
            endpoint: API端点
            method: HTTP方法
            data: 请求数据
            
        Returns:
            响应数据或None
        """
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Authorization': f"Bearer {self.api_key}",
            'Content-Type': 'application/json'
        }
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=data)
            else:
                response = requests.post(url, headers=headers, json=data or {})
                
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ API请求失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def start_browser(self, profile_id: Optional[str] = None, **kwargs) -> Optional[Dict]:
        """
        启动浏览器会话
        
        Args:
            profile_id: 配置文件ID
            **kwargs: 其他启动参数
            
        Returns:
            会话信息或None
        """
        print(f"🚀 启动指纹浏览器会话...")
        
        data = {}
        if profile_id:
            data['profile_id'] = profile_id
            print(f"   配置文件ID: {profile_id}")
            
        # 添加其他参数
        data.update(kwargs)
        
        response = self._make_request('/api/v1/browser/start', 'POST', data)

        if response:
            # 尝试不同的session_id字段名
            session_id = (response.get('session_id') or
                         response.get('sessionId') or
                         response.get('id') or
                         response.get('browser_id') or
                         response.get('browserId'))

            if session_id:
                self.active_sessions[session_id] = {
                    'profile_id': profile_id,
                    'start_time': time.time(),
                    'data': response
                }
                print(f"✅ 浏览器会话启动成功: {session_id}")
                return response
            else:
                print("❌ 响应中未找到session_id")
                print(f"   响应内容: {response}")
                # 如果没有session_id但有其他有用信息，仍然返回响应
                if response.get('status') == 'success' or response.get('code') == 0:
                    print("⚠️  会话可能已启动，但格式不标准")
                    # 生成一个临时session_id
                    temp_session_id = f"temp_{int(time.time())}"
                    self.active_sessions[temp_session_id] = {
                        'profile_id': profile_id,
                        'start_time': time.time(),
                        'data': response
                    }
                    response['session_id'] = temp_session_id
                    return response

        return None
    
    def stop_browser(self, session_id: str) -> bool:
        """
        停止浏览器会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否成功停止
        """
        print(f"🛑 停止浏览器会话: {session_id}")
        
        data = {'session_id': session_id}
        response = self._make_request('/api/v1/browser/stop', 'POST', data)
        
        if response:
            # 从活动会话中移除
            if session_id in self.active_sessions:
                session_info = self.active_sessions.pop(session_id)
                duration = time.time() - session_info['start_time']
                print(f"✅ 会话已停止，运行时长: {duration:.1f}秒")
            else:
                print("✅ 会话已停止")
            return True
        
        return False
    
    def get_browser_info(self, session_id: str) -> Optional[Dict]:
        """
        获取浏览器会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话信息或None
        """
        data = {'session_id': session_id}
        return self._make_request('/api/v1/browser/info', 'GET', data)
    
    def list_profiles(self) -> Optional[Dict]:
        """
        获取所有配置文件列表
        
        Returns:
            配置文件列表或None
        """
        return self._make_request('/api/v1/profiles', 'GET')
    
    def create_profile(self, name: str, **kwargs) -> Optional[Dict]:
        """
        创建新的配置文件
        
        Args:
            name: 配置文件名称
            **kwargs: 其他配置参数
            
        Returns:
            创建的配置文件信息或None
        """
        data = {'name': name}
        data.update(kwargs)
        
        response = self._make_request('/api/v1/profiles', 'POST', data)
        if response:
            print(f"✅ 配置文件创建成功: {name}")
        
        return response
    
    def delete_profile(self, profile_id: str) -> bool:
        """
        删除配置文件
        
        Args:
            profile_id: 配置文件ID
            
        Returns:
            是否成功删除
        """
        data = {'profile_id': profile_id}
        response = self._make_request('/api/v1/profiles/delete', 'POST', data)
        
        if response:
            print(f"✅ 配置文件已删除: {profile_id}")
            return True
        
        return False
    
    def get_active_sessions(self) -> Dict:
        """
        获取当前活动的会话列表
        
        Returns:
            活动会话字典
        """
        return self.active_sessions.copy()
    
    def stop_all_sessions(self) -> int:
        """
        停止所有活动会话
        
        Returns:
            成功停止的会话数量
        """
        stopped_count = 0
        session_ids = list(self.active_sessions.keys())
        
        for session_id in session_ids:
            if self.stop_browser(session_id):
                stopped_count += 1
        
        print(f"🛑 已停止 {stopped_count} 个会话")
        return stopped_count
    
    def health_check(self) -> bool:
        """
        检查指纹浏览器服务是否正常运行
        
        Returns:
            服务是否正常
        """
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ 指纹浏览器服务运行正常")
                return True
            else:
                print(f"⚠️  指纹浏览器服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到指纹浏览器服务: {e}")
            return False
    
    def __del__(self):
        """析构函数，确保清理所有会话"""
        if hasattr(self, 'active_sessions') and self.active_sessions:
            print("🧹 清理剩余的浏览器会话...")
            self.stop_all_sessions()


# 默认配置
DEFAULT_CONFIG = {
    'api_key': '6898fca8ed6927c4bbd74fab2f6f1357',
    'host': '127.0.0.1',
    'port': 50000
}

def create_browser_manager(api_key: Optional[str] = None, host: Optional[str] = None, port: Optional[int] = None) -> FingerprintBrowserManager:
    """
    创建指纹浏览器管理器实例
    
    Args:
        api_key: API密钥，默认使用配置中的值
        host: 主机地址，默认使用配置中的值
        port: 端口，默认使用配置中的值
        
    Returns:
        FingerprintBrowserManager实例
    """
    return FingerprintBrowserManager(
        api_key=api_key or DEFAULT_CONFIG['api_key'],
        host=host or DEFAULT_CONFIG['host'],
        port=port or DEFAULT_CONFIG['port']
    )


if __name__ == "__main__":
    # 测试代码
    print("🧪 指纹浏览器管理器测试")
    print("=" * 50)
    
    # 创建管理器
    manager = create_browser_manager()
    
    # 健康检查
    if manager.health_check():
        # 获取配置文件列表
        profiles = manager.list_profiles()
        if profiles:
            print(f"📋 可用配置文件: {len(profiles.get('profiles', []))}")
        
        # 启动一个测试会话
        session = manager.start_browser()
        if session:
            session_id = session.get('session_id')

            if session_id:
                # 等待几秒
                print("⏳ 等待5秒...")
                time.sleep(5)

                # 停止会话
                manager.stop_browser(session_id)
            else:
                print("⚠️  未获取到有效的session_id")
    
    print("🎉 测试完成")
