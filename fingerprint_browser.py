#!/usr/bin/env python3
"""
Roxy Browser API管理模块
用于与Roxy Browser指纹浏览器服务进行交互
"""

import requests
import json
import time
from typing import Optional, Dict, Any, List

class RoxyBrowserManager:
    """Roxy Browser指纹浏览器管理器"""

    def __init__(self, token: str, host: str = '127.0.0.1', port: int = 50000):
        """
        初始化Roxy Browser管理器

        Args:
            token: API Token
            host: 服务器主机地址
            port: 服务器端口
        """
        self.token = token
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.active_sessions = {}
        self.workspace_id = None  # 工作空间ID，需要通过API获取
        
    def _make_request(self, endpoint: str, method: str = 'POST', data: Optional[Dict] = None) -> Optional[Dict]:
        """
        发送API请求到Roxy Browser

        Args:
            endpoint: API端点
            method: HTTP方法
            data: 请求数据

        Returns:
            响应数据或None
        """
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Content-Type': 'application/json',
            'token': self.token,
            'User-Agent': 'RoxyBrowser-API-Client/1.0'
        }

        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=data, timeout=30)
            else:
                response = requests.post(url, headers=headers, json=data or {}, timeout=30)

            if response.status_code == 200:
                result = response.json()
                # 检查Roxy Browser的响应格式
                if result.get('code') == 0:
                    return result
                else:
                    print(f"❌ API返回错误: {result.get('msg', 'Unknown error')}")
                    return None
            else:
                print(f"❌ HTTP请求失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None

    def get_workspaces(self) -> Optional[Dict]:
        """
        获取工作空间列表

        Returns:
            工作空间信息或None
        """
        print("📋 获取工作空间列表...")
        response = self._make_request('/browser/workspace', 'GET')

        if response and response.get('data'):
            workspaces = response['data'].get('rows', [])
            if workspaces:
                # 自动选择第一个工作空间
                self.workspace_id = workspaces[0]['id']
                workspace_name = workspaces[0]['workspaceName']
                print(f"✅ 选择工作空间: {workspace_name} (ID: {self.workspace_id})")
                return response
            else:
                print("❌ 未找到工作空间")

        return None

    def get_browser_list(self, workspace_id: Optional[int] = None) -> Optional[Dict]:
        """
        获取浏览器窗口列表

        Args:
            workspace_id: 工作空间ID

        Returns:
            浏览器窗口列表或None
        """
        if not workspace_id:
            workspace_id = self.workspace_id

        if not workspace_id:
            print("❌ 未指定工作空间ID")
            return None

        data = {'workspaceId': workspace_id}
        return self._make_request('/browser/list_v3', 'GET', data)

    def start_browser(self, dir_id: Optional[str] = None, workspace_id: Optional[int] = None, **kwargs) -> Optional[Dict]:
        """
        启动浏览器窗口

        Args:
            dir_id: 浏览器窗口ID
            workspace_id: 工作空间ID
            **kwargs: 其他启动参数

        Returns:
            会话信息或None
        """
        print(f"🚀 启动浏览器窗口...")

        # 确保有工作空间ID
        if not workspace_id:
            workspace_id = self.workspace_id

        if not workspace_id:
            # 尝试获取工作空间
            if not self.get_workspaces():
                print("❌ 无法获取工作空间信息")
                return None
            workspace_id = self.workspace_id

        # 如果没有指定dir_id，尝试获取第一个可用的浏览器窗口
        if not dir_id:
            browser_list = self.get_browser_list(workspace_id)
            if browser_list and browser_list.get('data', {}).get('rows'):
                browsers = browser_list['data']['rows']
                if browsers:
                    dir_id = browsers[0]['dirId']
                    window_name = browsers[0].get('windowName', 'Unknown')
                    print(f"   自动选择浏览器窗口: {window_name} ({dir_id})")
                else:
                    print("❌ 未找到可用的浏览器窗口")
                    return None
            else:
                print("❌ 无法获取浏览器窗口列表")
                return None

        # 构建启动请求数据
        data = {
            'workspaceId': workspace_id,
            'dirId': dir_id
        }

        # 添加启动参数
        if kwargs.get('args'):
            data['args'] = kwargs['args']

        print(f"   工作空间ID: {workspace_id}")
        print(f"   浏览器窗口ID: {dir_id}")

        response = self._make_request('/browser/open', 'POST', data)

        if response and response.get('data'):
            session_data = response['data']
            session_id = dir_id  # 使用dir_id作为session_id

            self.active_sessions[session_id] = {
                'dir_id': dir_id,
                'workspace_id': workspace_id,
                'start_time': time.time(),
                'data': session_data
            }

            print(f"✅ 浏览器窗口启动成功: {dir_id}")
            print(f"   WebSocket: {session_data.get('ws', 'N/A')}")
            print(f"   HTTP调试端口: {session_data.get('http', 'N/A')}")
            print(f"   进程ID: {session_data.get('pid', 'N/A')}")

            # 返回标准化的响应格式
            return {
                'session_id': session_id,
                'dir_id': dir_id,
                'workspace_id': workspace_id,
                'ws': session_data.get('ws'),
                'http': session_data.get('http'),
                'pid': session_data.get('pid'),
                'driver': session_data.get('driver'),
                'window_name': session_data.get('windowName'),
                'window_remark': session_data.get('windowRemark')
            }

        return None
    
    def stop_browser(self, session_id: str) -> bool:
        """
        停止浏览器窗口

        Args:
            session_id: 会话ID (实际上是dir_id)

        Returns:
            是否成功停止
        """
        print(f"🛑 停止浏览器窗口: {session_id}")

        data = {'dirId': session_id}
        response = self._make_request('/browser/close', 'POST', data)

        if response:
            # 从活动会话中移除
            if session_id in self.active_sessions:
                session_info = self.active_sessions.pop(session_id)
                duration = time.time() - session_info['start_time']
                print(f"✅ 浏览器窗口已停止，运行时长: {duration:.1f}秒")
            else:
                print("✅ 浏览器窗口已停止")
            return True

        return False
    
    def get_browser_info(self, dir_id: str, workspace_id: Optional[int] = None) -> Optional[Dict]:
        """
        获取浏览器窗口详细信息

        Args:
            dir_id: 浏览器窗口ID
            workspace_id: 工作空间ID

        Returns:
            浏览器窗口信息或None
        """
        if not workspace_id:
            workspace_id = self.workspace_id

        if not workspace_id:
            print("❌ 未指定工作空间ID")
            return None

        data = {
            'workspaceId': workspace_id,
            'dirId': dir_id
        }
        return self._make_request('/browser/detail', 'GET', data)

    def get_connection_info(self, dir_ids: Optional[List[str]] = None) -> Optional[Dict]:
        """
        获取已打开窗口的连接信息

        Args:
            dir_ids: 浏览器窗口ID列表

        Returns:
            连接信息或None
        """
        data = {}
        if dir_ids:
            data['dirIds'] = ','.join(dir_ids)

        return self._make_request('/browser/connection_info', 'GET', data)

    def create_browser_window(self, window_name: str, workspace_id: Optional[int] = None, **kwargs) -> Optional[Dict]:
        """
        创建新的浏览器窗口

        Args:
            window_name: 窗口名称
            workspace_id: 工作空间ID
            **kwargs: 其他配置参数

        Returns:
            创建的窗口信息或None
        """
        if not workspace_id:
            workspace_id = self.workspace_id

        if not workspace_id:
            print("❌ 未指定工作空间ID")
            return None

        data = {
            'workspaceId': workspace_id,
            'windowName': window_name
        }
        data.update(kwargs)

        response = self._make_request('/browser/create', 'POST', data)
        if response and response.get('data'):
            dir_id = response['data'].get('dirId')
            print(f"✅ 浏览器窗口创建成功: {window_name} ({dir_id})")

        return response

    def delete_browser_windows(self, dir_ids: List[str], workspace_id: Optional[int] = None) -> bool:
        """
        删除浏览器窗口（支持批量）

        Args:
            dir_ids: 浏览器窗口ID列表
            workspace_id: 工作空间ID

        Returns:
            是否成功删除
        """
        if not workspace_id:
            workspace_id = self.workspace_id

        if not workspace_id:
            print("❌ 未指定工作空间ID")
            return False

        data = {
            'workspaceId': workspace_id,
            'dirIds': dir_ids
        }
        response = self._make_request('/browser/delete', 'POST', data)

        if response:
            print(f"✅ 已删除 {len(dir_ids)} 个浏览器窗口")
            return True

        return False
    
    def get_active_sessions(self) -> Dict:
        """
        获取当前活动的会话列表
        
        Returns:
            活动会话字典
        """
        return self.active_sessions.copy()
    
    def stop_all_sessions(self) -> int:
        """
        停止所有活动会话
        
        Returns:
            成功停止的会话数量
        """
        stopped_count = 0
        session_ids = list(self.active_sessions.keys())
        
        for session_id in session_ids:
            if self.stop_browser(session_id):
                stopped_count += 1
        
        print(f"🛑 已停止 {stopped_count} 个会话")
        return stopped_count
    
    def health_check(self) -> bool:
        """
        检查Roxy Browser服务是否正常运行

        Returns:
            服务是否正常
        """
        try:
            response = self._make_request('/health', 'GET')
            if response and response.get('code') == 0:
                print("✅ Roxy Browser服务运行正常")
                return True
            else:
                print(f"⚠️  Roxy Browser服务响应异常")
                return False
        except Exception as e:
            print(f"❌ 无法连接到Roxy Browser服务: {e}")
            return False
    
    def __del__(self):
        """析构函数，确保清理所有会话"""
        if hasattr(self, 'active_sessions') and self.active_sessions:
            print("🧹 清理剩余的浏览器会话...")
            self.stop_all_sessions()


# 默认配置
DEFAULT_CONFIG = {
    'token': '6898fca8ed6927c4bbd74fab2f6f1357',
    'host': '127.0.0.1',
    'port': 50000
}

def create_browser_manager(token: Optional[str] = None, host: Optional[str] = None, port: Optional[int] = None) -> RoxyBrowserManager:
    """
    创建Roxy Browser管理器实例

    Args:
        token: API Token，默认使用配置中的值
        host: 主机地址，默认使用配置中的值
        port: 端口，默认使用配置中的值

    Returns:
        RoxyBrowserManager实例
    """
    return RoxyBrowserManager(
        token=token or DEFAULT_CONFIG['token'],
        host=host or DEFAULT_CONFIG['host'],
        port=port or DEFAULT_CONFIG['port']
    )


if __name__ == "__main__":
    # 测试代码
    print("🧪 指纹浏览器管理器测试")
    print("=" * 50)
    
    # 创建管理器
    manager = create_browser_manager()
    
    # 健康检查
    if manager.health_check():
        # 获取工作空间列表
        workspaces = manager.get_workspaces()
        if workspaces:
            print(f"📋 工作空间获取成功")

        # 获取浏览器窗口列表
        browsers = manager.get_browser_list()
        if browsers and browsers.get('data', {}).get('rows'):
            browser_count = len(browsers['data']['rows'])
            print(f"📋 找到 {browser_count} 个浏览器窗口")

        # 启动一个测试会话
        session = manager.start_browser()
        if session:
            session_id = session.get('session_id')

            if session_id:
                # 等待几秒
                print("⏳ 等待5秒...")
                time.sleep(5)

                # 停止会话
                manager.stop_browser(session_id)
            else:
                print("⚠️  未获取到有效的session_id")
    
    print("🎉 测试完成")
